{"version": "1.0.0", "main": "./server/server.js", "scripts": {"dev:server": "nodemon ./server", "start:client": "npm start --prefix client", "start": "concurrently \"npm run dev:server\" \"npm run start:client\""}, "private": "true", "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "contentful-cli": "^1.12.17", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^8.2.0", "express": "^4.17.1", "express-mongo-sanitize": "^2.0.0", "express-rate-limit": "^5.1.3", "express-validator": "^6.6.1", "fs": "^0.0.1-security", "helmet": "^4.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.20", "mongoose": "^5.10.2", "node-fetch": "^3.3.2", "nodemailer": "^6.4.11", "path": "^0.12.7", "pokersolver": "^2.1.4", "request": "^2.88.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "underscore": "^1.11.0", "xss-clean": "^0.1.1"}, "devDependencies": {"concurrently": "^5.3.0", "core-js": "^3.6.5", "nodemon": "^2.0.4", "raf": "^3.4.1"}}