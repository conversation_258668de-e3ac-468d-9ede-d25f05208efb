{"version": "1.0.0", "private": true, "license": "UNLICENSED", "dependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^0.20.0", "bootstrap": "^5.1.3", "contentful": "^7.14.6", "ethers": "^5.6.4", "react": "^16.13.1", "react-bootstrap": "^2.2.3", "react-dom": "^16.13.1", "react-remarkable": "^1.1.3", "react-router-dom": "^6.3.0", "react-scripts": "^3.4.3", "sass": "^1.50.0", "socket.io-client": "^4.8.1", "styled-components": "^5.1.1", "sweetalert2": "^11.4.8", "zustand": "^4.0.0-rc.0"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "build": "react-scripts --openssl-legacy-provider build", "test": "react-scripts --openssl-legacy-provider test", "eject": "react-scripts --openssl-legacy-provider eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:7777"}