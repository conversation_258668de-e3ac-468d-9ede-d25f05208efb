import React, { useState, useEffect, useContext } from "react";
import SocketContext from "./socketContext";
import io from "socket.io-client";
import { useNavigate } from "react-router-dom";
import {
  CS_DISCONNECT,
  CS_FETCH_LOBBY_INFO,
  SC_PLAYERS_UPDATED,
  SC_RECEIVE_LOBBY_INFO,
  SC_TABLES_UPDATED,
} from "../../pokergame/actions";
import globalContext from "../global/globalContext";
import config from "../../clientConfig";

const WebSocketProvider = ({ children }) => {
  const { setTables, setPlayers, setChipsAmount } = useContext(globalContext);
  const navigate = useNavigate();

  const [socket, setSocket] = useState(null);
  const [socketId, setSocketId] = useState(null);

  // Debug: Check if io is imported correctly
  console.log("Socket.IO client imported:", typeof io, io);

  useEffect(() => {
    window.addEventListener("beforeunload", cleanUp);
    window.addEventListener("beforeclose", cleanUp);
    return () => cleanUp();
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    console.log("socket context - initializing");
    if (!socket) {
      console.log("No socket exists, creating new connection");
      connect();
      // Don't set socket here - let the 'connect' event handler do it
    } else {
      console.log("Socket already exists:", socket.connected);
    }

    return () => cleanUp();
    // eslint-disable-next-line
  }, []);

  function cleanUp() {
    window.socket && window.socket.emit(CS_DISCONNECT);
    window.socket && window.socket.close();
    setSocket(null);
    setSocketId(null);
    setPlayers(null);
    setTables(null);
  }

  function connect() {
    console.log("Attempting to connect to socket at:", config.socketURI);
    console.log("Config object:", config);
    console.log("Window location hostname:", window.location.hostname);

    const socket = io(config.socketURI, {
      transports: ["websocket"],
      upgrade: false,
    });

    console.log("Socket instance created:", socket);
    registerCallbacks(socket);
    window.socket = socket;
    return socket;
  }

  function registerCallbacks(socket) {
    console.log("Registering socket callbacks for socket:", socket.id);

    socket.on("connect", () => {
      console.log("Socket 'connect' event fired! Setting socket in state");
      setSocket(socket);
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
    });

    socket.on("disconnect", (reason) => {
      console.log("Socket disconnected:", reason);
    });

    socket.on(
      SC_RECEIVE_LOBBY_INFO,
      ({ tables, players, socketId, amount }) => {
        console.log(SC_RECEIVE_LOBBY_INFO, tables, players, socketId);
        setSocketId(socketId);
        setChipsAmount(amount);
        setTables(tables);
        setPlayers(players);
      }
    );

    socket.on(SC_PLAYERS_UPDATED, (players) => {
      console.log(SC_PLAYERS_UPDATED, players);
      setPlayers(players);
    });

    socket.on(SC_TABLES_UPDATED, (tables) => {
      console.log(SC_TABLES_UPDATED, tables);
      setTables(tables);
    });
  }

  return (
    <SocketContext.Provider value={{ socket, socketId, cleanUp }}>
      {children}
    </SocketContext.Provider>
  );
};

export default WebSocketProvider;
