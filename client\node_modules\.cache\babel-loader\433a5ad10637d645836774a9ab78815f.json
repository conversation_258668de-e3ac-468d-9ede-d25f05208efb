{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Multibank-Crypto-Poker\\\\client\\\\src\\\\pages\\\\ConnectWallet\\\\ConnectWallet.js\";\nimport React, { useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useLocation } from \"react-router-dom\";\nimport Swal from \"sweetalert2\";\nimport globalContext from \"./../../context/global/globalContext\";\nimport LoadingScreen from \"../../components/loading/LoadingScreen\";\nimport socketContext from \"../../context/websocket/socketContext\";\nimport { CS_FETCH_LOBBY_INFO } from \"../../pokergame/actions\";\nimport \"./ConnectWallet.scss\";\nconst ConnectWallet = () => {\n  const {\n    setWalletAddress,\n    setChipsAmount\n  } = useContext(globalContext);\n  const {\n    socket\n  } = useContext(socketContext);\n  const navigate = useNavigate();\n  const useQuery = () => new URLSearchParams(useLocation().search);\n  let query = useQuery();\n  useEffect(() => {\n    console.log(\"ConnectWallet useEffect - socket:\", socket === null || socket === void 0 ? void 0 : socket.connected, \"socket exists:\", !!socket);\n    if (socket !== null && socket.connected === true) {\n      const walletAddress = query.get(\"walletAddress\");\n      const gameId = query.get(\"gameId\");\n      const username = query.get(\"username\");\n      console.log(\"URL Parameters:\", {\n        walletAddress,\n        gameId,\n        username\n      });\n      if (walletAddress && gameId && username) {\n        console.log(\"All parameters present, proceeding with connection\");\n        setWalletAddress(walletAddress);\n        socket.emit(CS_FETCH_LOBBY_INFO, {\n          walletAddress,\n          socketId: socket.id,\n          gameId,\n          username\n        });\n        console.log(CS_FETCH_LOBBY_INFO, {\n          walletAddress,\n          socketId: socket.id,\n          gameId,\n          username\n        });\n        navigate(\"/play\");\n      } else {\n        console.log(\"Missing required URL parameters - staying on loading screen\");\n      }\n    } else {\n      console.log(\"Socket not connected yet\");\n    }\n  }, [socket]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(LoadingScreen, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }\n  }));\n};\nexport default ConnectWallet;", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useNavigate", "useLocation", "<PERSON><PERSON>", "globalContext", "LoadingScreen", "socketContext", "CS_FETCH_LOBBY_INFO", "ConnectWallet", "setWalletAddress", "setChipsAmount", "socket", "navigate", "useQuery", "URLSearchParams", "search", "query", "console", "log", "connected", "wallet<PERSON>ddress", "get", "gameId", "username", "emit", "socketId", "id", "createElement", "Fragment", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/Multibank-Crypto-Poker/client/src/pages/ConnectWallet/ConnectWallet.js"], "sourcesContent": ["import React, { useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport Swal from \"sweetalert2\";\r\nimport globalContext from \"./../../context/global/globalContext\";\r\nimport LoadingScreen from \"../../components/loading/LoadingScreen\";\r\n\r\nimport socketContext from \"../../context/websocket/socketContext\";\r\nimport { CS_FETCH_LOBBY_INFO } from \"../../pokergame/actions\";\r\nimport \"./ConnectWallet.scss\";\r\n\r\nconst ConnectWallet = () => {\r\n  const { setWalletAddress, setChipsAmount } = useContext(globalContext);\r\n\r\n  const { socket } = useContext(socketContext);\r\n  const navigate = useNavigate();\r\n  const useQuery = () => new URLSearchParams(useLocation().search);\r\n  let query = useQuery();\r\n\r\n  useEffect(() => {\r\n    console.log(\r\n      \"ConnectWallet useEffect - socket:\",\r\n      socket?.connected,\r\n      \"socket exists:\",\r\n      !!socket\r\n    );\r\n\r\n    if (socket !== null && socket.connected === true) {\r\n      const walletAddress = query.get(\"walletAddress\");\r\n      const gameId = query.get(\"gameId\");\r\n      const username = query.get(\"username\");\r\n\r\n      console.log(\"URL Parameters:\", { walletAddress, gameId, username });\r\n\r\n      if (walletAddress && gameId && username) {\r\n        console.log(\"All parameters present, proceeding with connection\");\r\n        setWalletAddress(walletAddress);\r\n        socket.emit(CS_FETCH_LOBBY_INFO, {\r\n          walletAddress,\r\n          socketId: socket.id,\r\n          gameId,\r\n          username,\r\n        });\r\n        console.log(CS_FETCH_LOBBY_INFO, {\r\n          walletAddress,\r\n          socketId: socket.id,\r\n          gameId,\r\n          username,\r\n        });\r\n        navigate(\"/play\");\r\n      } else {\r\n        console.log(\r\n          \"Missing required URL parameters - staying on loading screen\"\r\n        );\r\n      }\r\n    } else {\r\n      console.log(\"Socket not connected yet\");\r\n    }\r\n  }, [socket]);\r\n\r\n  return (\r\n    <>\r\n      <LoadingScreen />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ConnectWallet;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACpD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,aAAa,MAAM,wCAAwC;AAElE,OAAOC,aAAa,MAAM,uCAAuC;AACjE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAO,sBAAsB;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGX,UAAU,CAACK,aAAa,CAAC;EAEtE,MAAM;IAAEO;EAAO,CAAC,GAAGZ,UAAU,CAACO,aAAa,CAAC;EAC5C,MAAMM,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGA,CAAA,KAAM,IAAIC,eAAe,CAACZ,WAAW,CAAC,CAAC,CAACa,MAAM,CAAC;EAChE,IAAIC,KAAK,GAAGH,QAAQ,CAAC,CAAC;EAEtBb,SAAS,CAAC,MAAM;IACdiB,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnCP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,SAAS,EACjB,gBAAgB,EAChB,CAAC,CAACR,MACJ,CAAC;IAED,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACQ,SAAS,KAAK,IAAI,EAAE;MAChD,MAAMC,aAAa,GAAGJ,KAAK,CAACK,GAAG,CAAC,eAAe,CAAC;MAChD,MAAMC,MAAM,GAAGN,KAAK,CAACK,GAAG,CAAC,QAAQ,CAAC;MAClC,MAAME,QAAQ,GAAGP,KAAK,CAACK,GAAG,CAAC,UAAU,CAAC;MAEtCJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAAEE,aAAa;QAAEE,MAAM;QAAEC;MAAS,CAAC,CAAC;MAEnE,IAAIH,aAAa,IAAIE,MAAM,IAAIC,QAAQ,EAAE;QACvCN,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjET,gBAAgB,CAACW,aAAa,CAAC;QAC/BT,MAAM,CAACa,IAAI,CAACjB,mBAAmB,EAAE;UAC/Ba,aAAa;UACbK,QAAQ,EAAEd,MAAM,CAACe,EAAE;UACnBJ,MAAM;UACNC;QACF,CAAC,CAAC;QACFN,OAAO,CAACC,GAAG,CAACX,mBAAmB,EAAE;UAC/Ba,aAAa;UACbK,QAAQ,EAAEd,MAAM,CAACe,EAAE;UACnBJ,MAAM;UACNC;QACF,CAAC,CAAC;QACFX,QAAQ,CAAC,OAAO,CAAC;MACnB,CAAC,MAAM;QACLK,OAAO,CAACC,GAAG,CACT,6DACF,CAAC;MACH;IACF,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;EAEZ,oBACEb,KAAA,CAAA6B,aAAA,CAAA7B,KAAA,CAAA8B,QAAA,qBACE9B,KAAA,CAAA6B,aAAA,CAACtB,aAAa;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChB,CAAC;AAEP,CAAC;AAED,eAAe1B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}