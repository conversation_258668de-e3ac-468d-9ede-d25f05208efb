import React, { useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import Swal from "sweetalert2";
import globalContext from "./../../context/global/globalContext";
import LoadingScreen from "../../components/loading/LoadingScreen";

import socketContext from "../../context/websocket/socketContext";
import { CS_FETCH_LOBBY_INFO } from "../../pokergame/actions";
import "./ConnectWallet.scss";

const ConnectWallet = () => {
  const { setWalletAddress, setChipsAmount } = useContext(globalContext);

  const { socket } = useContext(socketContext);
  const navigate = useNavigate();
  const useQuery = () => new URLSearchParams(useLocation().search);
  let query = useQuery();

  useEffect(() => {
    console.log(
      "ConnectWallet useEffect - socket:",
      socket?.connected,
      "socket exists:",
      !!socket
    );

    if (socket !== null && socket.connected === true) {
      const walletAddress = query.get("walletAddress");
      const gameId = query.get("gameId");
      const username = query.get("username");

      console.log("URL Parameters:", { walletAddress, gameId, username });

      if (walletAddress && gameId && username) {
        console.log("All parameters present, proceeding with connection");
        setWalletAddress(walletAddress);
        socket.emit(CS_FETCH_LOBBY_INFO, {
          walletAddress,
          socketId: socket.id,
          gameId,
          username,
        });
        console.log(CS_FETCH_LOBBY_INFO, {
          walletAddress,
          socketId: socket.id,
          gameId,
          username,
        });
        navigate("/play");
      } else {
        console.log(
          "Missing required URL parameters - staying on loading screen"
        );
      }
    } else {
      console.log("Socket not connected yet");
    }
  }, [socket]);

  return (
    <>
      <LoadingScreen />
    </>
  );
};

export default ConnectWallet;
