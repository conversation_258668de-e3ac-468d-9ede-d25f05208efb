const http = require("http");
const express = require("express");
const { Server } = require("socket.io");

const config = require("./config");
const configureMiddleware = require("./middleware");
const configureRoutes = require("./routes");
const gameSocket = require("./socket/index");

// Init express app
const app = express();

// Config Express-Middleware
configureMiddleware(app);

// Set-up Routes
configureRoutes(app);

// Create HTTP server explicitly (preferred with Socket.IO)
const server = http.createServer(app);

// Create Socket.IO server with CORS
const io = new Server(server, {
  allowEIO3: true,
  cors: {
    origin: [
      "http://localhost:3000", // add your frontend origins
      "http://127.0.0.1:3000",
      // "https://your-domain.com"
    ],
    methods: ["GET", "POST"],
    credentials: true,
  },
});

// Use the correct event name: "connection"
io.on("connection", (socket) => {
  // optional: debug logging
  console.log(`Socket connected: ${socket.id}`);
  gameSocket.init(socket, io);
});

// Start server
server.listen(config.PORT, () => {
  console.log(
    `Server is running in ${config.NODE_ENV} mode and listening on port ${config.PORT}...`
  );
});

// Error handling - close server
process.on("unhandledRejection", (err) => {
  console.error(`UnhandledRejection: ${err?.message || err}`);
  server.close(() => process.exit(1));
});

process.on("uncaughtException", (err) => {
  console.error(`UncaughtException: ${err?.message || err}`);
  server.close(() => process.exit(1));
});