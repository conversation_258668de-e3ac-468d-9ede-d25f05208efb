{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Multibank-Crypto-Poker\\\\client\\\\src\\\\context\\\\websocket\\\\WebsocketProvider.js\";\nimport React, { useState, useEffect, useContext } from \"react\";\nimport SocketContext from \"./socketContext\";\nimport io from \"socket.io-client\";\nimport { useNavigate } from \"react-router-dom\";\nimport { CS_DISCONNECT, CS_FETCH_LOBBY_INFO, SC_PLAYERS_UPDATED, SC_RECEIVE_LOBBY_INFO, SC_TABLES_UPDATED } from \"../../pokergame/actions\";\nimport globalContext from \"../global/globalContext\";\nimport config from \"../../clientConfig\";\nconst WebSocketProvider = ({\n  children\n}) => {\n  const {\n    setTables,\n    setPlayers,\n    setChipsAmount\n  } = useContext(globalContext);\n  const navigate = useNavigate();\n  const [socket, setSocket] = useState(null);\n  const [socketId, setSocketId] = useState(null);\n  useEffect(() => {\n    window.addEventListener(\"beforeunload\", cleanUp);\n    window.addEventListener(\"beforeclose\", cleanUp);\n    return () => cleanUp();\n    // eslint-disable-next-line\n  }, []);\n  useEffect(() => {\n    console.log(\"socket context\");\n    const webSocket = socket || connect();\n    return () => cleanUp();\n    // eslint-disable-next-line\n  }, []);\n  function cleanUp() {\n    window.socket && window.socket.emit(CS_DISCONNECT);\n    window.socket && window.socket.close();\n    setSocket(null);\n    setSocketId(null);\n    setPlayers(null);\n    setTables(null);\n  }\n  function connect() {\n    console.log(\"Attempting to connect to socket at:\", config.socketURI);\n    const socket = io(config.socketURI, {\n      transports: [\"websocket\"],\n      upgrade: false\n    });\n    registerCallbacks(socket);\n    window.socket = socket;\n    return socket;\n  }\n  function registerCallbacks(socket) {\n    socket.on(\"connect\", () => {\n      console.log(\"Socket connected successfully!\");\n      setSocket(socket);\n    });\n    socket.on(\"connect_error\", error => {\n      console.error(\"Socket connection error:\", error);\n    });\n    socket.on(\"disconnect\", reason => {\n      console.log(\"Socket disconnected:\", reason);\n    });\n    socket.on(SC_RECEIVE_LOBBY_INFO, ({\n      tables,\n      players,\n      socketId,\n      amount\n    }) => {\n      console.log(SC_RECEIVE_LOBBY_INFO, tables, players, socketId);\n      setSocketId(socketId);\n      setChipsAmount(amount);\n      setTables(tables);\n      setPlayers(players);\n    });\n    socket.on(SC_PLAYERS_UPDATED, players => {\n      console.log(SC_PLAYERS_UPDATED, players);\n      setPlayers(players);\n    });\n    socket.on(SC_TABLES_UPDATED, tables => {\n      console.log(SC_TABLES_UPDATED, tables);\n      setTables(tables);\n    });\n  }\n  return /*#__PURE__*/React.createElement(SocketContext.Provider, {\n    value: {\n      socket,\n      socketId,\n      cleanUp\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 5\n    }\n  }, children);\n};\nexport default WebSocketProvider;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "SocketContext", "io", "useNavigate", "CS_DISCONNECT", "CS_FETCH_LOBBY_INFO", "SC_PLAYERS_UPDATED", "SC_RECEIVE_LOBBY_INFO", "SC_TABLES_UPDATED", "globalContext", "config", "WebSocketProvider", "children", "setTables", "setPlayers", "setChipsAmount", "navigate", "socket", "setSocket", "socketId", "setSocketId", "window", "addEventListener", "cleanUp", "console", "log", "webSocket", "connect", "emit", "close", "socketURI", "transports", "upgrade", "registerCallbacks", "on", "error", "reason", "tables", "players", "amount", "createElement", "Provider", "value", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/Multibank-Crypto-Poker/client/src/context/websocket/WebsocketProvider.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport SocketContext from \"./socketContext\";\r\nimport io from \"socket.io-client\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport {\r\n  CS_DISCONNECT,\r\n  CS_FETCH_LOBBY_INFO,\r\n  SC_PLAYERS_UPDATED,\r\n  SC_RECEIVE_LOBBY_INFO,\r\n  SC_TABLES_UPDATED,\r\n} from \"../../pokergame/actions\";\r\nimport globalContext from \"../global/globalContext\";\r\nimport config from \"../../clientConfig\";\r\n\r\nconst WebSocketProvider = ({ children }) => {\r\n  const { setTables, setPlayers, setChipsAmount } = useContext(globalContext);\r\n  const navigate = useNavigate();\r\n\r\n  const [socket, setSocket] = useState(null);\r\n  const [socketId, setSocketId] = useState(null);\r\n\r\n  useEffect(() => {\r\n    window.addEventListener(\"beforeunload\", cleanUp);\r\n    window.addEventListener(\"beforeclose\", cleanUp);\r\n    return () => cleanUp();\r\n    // eslint-disable-next-line\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    console.log(\"socket context\");\r\n    const webSocket = socket || connect();\r\n\r\n    return () => cleanUp();\r\n    // eslint-disable-next-line\r\n  }, []);\r\n\r\n  function cleanUp() {\r\n    window.socket && window.socket.emit(CS_DISCONNECT);\r\n    window.socket && window.socket.close();\r\n    setSocket(null);\r\n    setSocketId(null);\r\n    setPlayers(null);\r\n    setTables(null);\r\n  }\r\n\r\n  function connect() {\r\n    console.log(\"Attempting to connect to socket at:\", config.socketURI);\r\n    const socket = io(config.socketURI, {\r\n      transports: [\"websocket\"],\r\n      upgrade: false,\r\n    });\r\n    registerCallbacks(socket);\r\n    window.socket = socket;\r\n    return socket;\r\n  }\r\n\r\n  function registerCallbacks(socket) {\r\n    socket.on(\"connect\", () => {\r\n      console.log(\"Socket connected successfully!\");\r\n      setSocket(socket);\r\n    });\r\n\r\n    socket.on(\"connect_error\", (error) => {\r\n      console.error(\"Socket connection error:\", error);\r\n    });\r\n\r\n    socket.on(\"disconnect\", (reason) => {\r\n      console.log(\"Socket disconnected:\", reason);\r\n    });\r\n\r\n    socket.on(\r\n      SC_RECEIVE_LOBBY_INFO,\r\n      ({ tables, players, socketId, amount }) => {\r\n        console.log(SC_RECEIVE_LOBBY_INFO, tables, players, socketId);\r\n        setSocketId(socketId);\r\n        setChipsAmount(amount);\r\n        setTables(tables);\r\n        setPlayers(players);\r\n      }\r\n    );\r\n\r\n    socket.on(SC_PLAYERS_UPDATED, (players) => {\r\n      console.log(SC_PLAYERS_UPDATED, players);\r\n      setPlayers(players);\r\n    });\r\n\r\n    socket.on(SC_TABLES_UPDATED, (tables) => {\r\n      console.log(SC_TABLES_UPDATED, tables);\r\n      setTables(tables);\r\n    });\r\n  }\r\n\r\n  return (\r\n    <SocketContext.Provider value={{ socket, socketId, cleanUp }}>\r\n      {children}\r\n    </SocketContext.Provider>\r\n  );\r\n};\r\n\r\nexport default WebSocketProvider;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,aAAa,EACbC,mBAAmB,EACnBC,kBAAkB,EAClBC,qBAAqB,EACrBC,iBAAiB,QACZ,yBAAyB;AAChC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,MAAM,MAAM,oBAAoB;AAEvC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC1C,MAAM;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EAAe,CAAC,GAAGf,UAAU,CAACS,aAAa,CAAC;EAC3E,MAAMO,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACdsB,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEC,OAAO,CAAC;IAChDF,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEC,OAAO,CAAC;IAC/C,OAAO,MAAMA,OAAO,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAENxB,SAAS,CAAC,MAAM;IACdyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMC,SAAS,GAAGT,MAAM,IAAIU,OAAO,CAAC,CAAC;IAErC,OAAO,MAAMJ,OAAO,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,SAASA,OAAOA,CAAA,EAAG;IACjBF,MAAM,CAACJ,MAAM,IAAII,MAAM,CAACJ,MAAM,CAACW,IAAI,CAACxB,aAAa,CAAC;IAClDiB,MAAM,CAACJ,MAAM,IAAII,MAAM,CAACJ,MAAM,CAACY,KAAK,CAAC,CAAC;IACtCX,SAAS,CAAC,IAAI,CAAC;IACfE,WAAW,CAAC,IAAI,CAAC;IACjBN,UAAU,CAAC,IAAI,CAAC;IAChBD,SAAS,CAAC,IAAI,CAAC;EACjB;EAEA,SAASc,OAAOA,CAAA,EAAG;IACjBH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEf,MAAM,CAACoB,SAAS,CAAC;IACpE,MAAMb,MAAM,GAAGf,EAAE,CAACQ,MAAM,CAACoB,SAAS,EAAE;MAClCC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,OAAO,EAAE;IACX,CAAC,CAAC;IACFC,iBAAiB,CAAChB,MAAM,CAAC;IACzBI,MAAM,CAACJ,MAAM,GAAGA,MAAM;IACtB,OAAOA,MAAM;EACf;EAEA,SAASgB,iBAAiBA,CAAChB,MAAM,EAAE;IACjCA,MAAM,CAACiB,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBV,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7CP,SAAS,CAACD,MAAM,CAAC;IACnB,CAAC,CAAC;IAEFA,MAAM,CAACiB,EAAE,CAAC,eAAe,EAAGC,KAAK,IAAK;MACpCX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,CAAC;IAEFlB,MAAM,CAACiB,EAAE,CAAC,YAAY,EAAGE,MAAM,IAAK;MAClCZ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEW,MAAM,CAAC;IAC7C,CAAC,CAAC;IAEFnB,MAAM,CAACiB,EAAE,CACP3B,qBAAqB,EACrB,CAAC;MAAE8B,MAAM;MAAEC,OAAO;MAAEnB,QAAQ;MAAEoB;IAAO,CAAC,KAAK;MACzCf,OAAO,CAACC,GAAG,CAAClB,qBAAqB,EAAE8B,MAAM,EAAEC,OAAO,EAAEnB,QAAQ,CAAC;MAC7DC,WAAW,CAACD,QAAQ,CAAC;MACrBJ,cAAc,CAACwB,MAAM,CAAC;MACtB1B,SAAS,CAACwB,MAAM,CAAC;MACjBvB,UAAU,CAACwB,OAAO,CAAC;IACrB,CACF,CAAC;IAEDrB,MAAM,CAACiB,EAAE,CAAC5B,kBAAkB,EAAGgC,OAAO,IAAK;MACzCd,OAAO,CAACC,GAAG,CAACnB,kBAAkB,EAAEgC,OAAO,CAAC;MACxCxB,UAAU,CAACwB,OAAO,CAAC;IACrB,CAAC,CAAC;IAEFrB,MAAM,CAACiB,EAAE,CAAC1B,iBAAiB,EAAG6B,MAAM,IAAK;MACvCb,OAAO,CAACC,GAAG,CAACjB,iBAAiB,EAAE6B,MAAM,CAAC;MACtCxB,SAAS,CAACwB,MAAM,CAAC;IACnB,CAAC,CAAC;EACJ;EAEA,oBACExC,KAAA,CAAA2C,aAAA,CAACvC,aAAa,CAACwC,QAAQ;IAACC,KAAK,EAAE;MAAEzB,MAAM;MAAEE,QAAQ;MAAEI;IAAQ,CAAE;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DpC,QACqB,CAAC;AAE7B,CAAC;AAED,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}